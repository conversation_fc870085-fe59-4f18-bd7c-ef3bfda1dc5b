// Utility to test backend API endpoints
export const testApiEndpoints = async () => {
  const baseUrl = 'http://localhost:5057';
  const endpoints = [
    '/api/auth/login',
    '/api/auth/google',
    '/api/auth/google/client-id',
    '/Auth/login',
    '/Auth/google-login',
    '/Config/google-client-id',
    '/auth/login',
    '/auth/google',
    '/auth/google/client-id'
  ];

  console.log('🔍 Testing backend API endpoints...');
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'OPTIONS',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log(`✅ ${endpoint}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`❌ ${endpoint}: Error - ${error}`);
    }
  }
};

// Test Google OAuth endpoint specifically
export const testGoogleEndpoint = async (idToken: string = 'test-token') => {
  const baseUrl = 'http://localhost:5057';
  const endpoints = [
    '/api/auth/google',
    '/Auth/google-login',
    '/auth/google'
  ];

  console.log('🔍 Testing Google OAuth endpoints...');
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ idToken }),
      });
      
      console.log(`✅ ${endpoint}: ${response.status} ${response.statusText}`);
      if (response.status !== 404) {
        const text = await response.text();
        console.log(`   Response: ${text.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint}: Error - ${error}`);
    }
  }
};
